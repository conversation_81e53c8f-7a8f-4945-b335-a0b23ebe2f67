import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  TextInputProps,
} from 'react-native';

/**
 * M3E Text Field 组件的属性接口
 */
export interface M3ETextFieldProps extends Omit<TextInputProps, 'style'> {
  /** 标签文本 */
  label?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 帮助文本 */
  supportingText?: string;
  /** 是否为错误状态 */
  error?: boolean;
  /** 错误信息 */
  errorText?: string;
  /** 样式变体 */
  variant?: 'filled' | 'outlined';
  /** 前置图标 */
  leadingIcon?: React.ReactNode;
  /** 后置图标 */
  trailingIcon?: React.ReactNode;
  /** 后置图标点击事件 */
  onTrailingIconPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Text Field 的样式类
 */
const getTextFieldStyles = (
  variant: string,
  isFocused: boolean,
  error: boolean,
  disabled: boolean,
  hasValue: boolean
) => {
  const baseContainer = 'relative';
  const baseInput = 'text-base text-gray-900 dark:text-white flex-1';

  if (variant === 'filled') {
    return {
      container: baseContainer,
      inputContainer: `flex-row items-center px-4 pt-6 pb-2 rounded-t bg-m3-surface-container ${
        disabled
          ? 'opacity-50'
          : isFocused
          ? error
            ? 'border-b-2 border-m3-error-main'
            : 'border-b-2 border-m3-primary-main'
          : error
          ? 'border-b border-m3-error-main'
          : 'border-b border-m3-outline-variant'
      }`,
      input: `${baseInput} ${disabled ? 'opacity-50' : ''}`,
      label: `absolute left-4 transition-all duration-200 ${
        isFocused || hasValue ? 'top-2 text-xs' : 'top-4 text-base'
      } ${
        disabled
          ? 'text-m3-surface-on-variant opacity-50'
          : error
          ? 'text-m3-error-main'
          : isFocused
          ? 'text-m3-primary-main'
          : 'text-m3-surface-on-variant'
      }`,
      supportingText: `mt-1 px-4 text-xs ${
        error ? 'text-m3-error-main' : 'text-m3-surface-on-variant'
      }`,
    };
  } else {
    // Outlined variant
    return {
      container: baseContainer,
      inputContainer: `flex-row items-center px-4 py-4 rounded border ${
        disabled
          ? 'border-m3-outline-variant opacity-50'
          : isFocused
          ? error
            ? 'border-2 border-m3-error-main'
            : 'border-2 border-m3-primary-main'
          : error
          ? 'border-m3-error-main'
          : 'border-m3-outline-variant'
      } bg-transparent`,
      input: `${baseInput} ${disabled ? 'opacity-50' : ''}`,
      label: `absolute left-3 px-1 bg-m3-surface-main transition-all duration-200 ${
        isFocused || hasValue ? '-top-2 text-xs' : 'top-4 text-base'
      } ${
        disabled
          ? 'text-m3-surface-on-variant opacity-50'
          : error
          ? 'text-m3-error-main'
          : isFocused
          ? 'text-m3-primary-main'
          : 'text-m3-surface-on-variant'
      }`,
      supportingText: `mt-1 px-4 text-xs ${
        error ? 'text-m3-error-main' : 'text-m3-surface-on-variant'
      }`,
    };
  }
};

/**
 * M3E Text Field 组件
 *
 * 基于 Material Design 3 规范的文本输入框组件，支持填充和轮廓两种样式。
 *
 * @example
 * ```tsx
 * const [value, setValue] = useState('');
 *
 * <M3ETextField
 *   label="Email"
 *   placeholder="Enter your email"
 *   value={value}
 *   onChangeText={setValue}
 *   variant="outlined"
 *   leadingIcon={<Icon name="email" />}
 *   supportingText="We'll never share your email"
 * />
 *
 * // 错误状态
 * <M3ETextField
 *   label="Password"
 *   placeholder="Enter password"
 *   value={value}
 *   onChangeText={setValue}
 *   variant="filled"
 *   error={true}
 *   errorText="Password is required"
 *   secureTextEntry={true}
 * />
 * ```
 */
export const M3ETextField: React.FC<M3ETextFieldProps> = ({
  label,
  placeholder,
  supportingText,
  error = false,
  errorText,
  variant = 'outlined',
  leadingIcon,
  trailingIcon,
  onTrailingIconPress,
  disabled = false,
  value,
  onChangeText,
  onFocus,
  onBlur,
  className = '',
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const hasValue = Boolean(value);
  const styles = getTextFieldStyles(
    variant,
    isFocused,
    error,
    disabled,
    hasValue
  );
  const displaySupportingText = error && errorText ? errorText : supportingText;

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleLabelPress = () => {
    if (!disabled) {
      inputRef.current?.focus();
    }
  };

  const containerClasses = `${styles.container} ${className}`;

  return (
    <View className={containerClasses}>
      {/* 输入框容器 */}
      <View className={styles.inputContainer}>
        {/* 前置图标 */}
        {leadingIcon && (
          <View className="mr-3 w-6 h-6 items-center justify-center">
            {leadingIcon}
          </View>
        )}

        {/* 输入框 */}
        <TextInput
          ref={inputRef}
          className={styles.input}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={!label && !isFocused ? placeholder : undefined}
          placeholderTextColor="#9CA3AF"
          editable={!disabled}
          {...textInputProps}
        />

        {/* 后置图标 */}
        {trailingIcon && (
          <TouchableOpacity
            className="ml-3 w-6 h-6 items-center justify-center"
            onPress={onTrailingIconPress}
            disabled={disabled}
            activeOpacity={0.7}
          >
            {trailingIcon}
          </TouchableOpacity>
        )}
      </View>

      {/* 标签 */}
      {label && (
        <TouchableOpacity
          className={styles.label}
          onPress={handleLabelPress}
          activeOpacity={1}
        >
          <Text className="text-inherit">{label}</Text>
        </TouchableOpacity>
      )}

      {/* 帮助文本 */}
      {displaySupportingText && (
        <Text className={styles.supportingText}>{displaySupportingText}</Text>
      )}
    </View>
  );
};

/**
 * M3E Text Field 变体组件
 */

/**
 * 填充样式文本框
 */
export const M3ETextFieldFilled: React.FC<M3ETextFieldProps> = (props) => (
  <M3ETextField {...props} variant="filled" />
);

/**
 * 轮廓样式文本框
 */
export const M3ETextFieldOutlined: React.FC<M3ETextFieldProps> = (props) => (
  <M3ETextField {...props} variant="outlined" />
);

export default M3ETextField;
