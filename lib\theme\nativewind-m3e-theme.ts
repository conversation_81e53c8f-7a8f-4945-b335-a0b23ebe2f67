/**
 * NativeWind M3E 主题系统
 * 基于 Material Design 3 Expressive 规范和 NativeWind 最佳实践
 */

import { vars } from 'nativewind';

// M3E 颜色令牌定义
export const M3E_COLOR_TOKENS = {
  light: {
    // Primary colors
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
    
    // Secondary colors
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',
    
    // Tertiary colors
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',
    
    // Error colors
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',
    
    // Background colors
    background: '#FEF7FF',
    onBackground: '#1D1B20',
    
    // Surface colors
    surface: '#FEF7FF',
    onSurface: '#1D1B20',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    surfaceContainer: '#F3EDF7',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    
    // Outline colors
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    
    // Other colors
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#322F35',
    inverseOnSurface: '#F5EFF7',
    inversePrimary: '#D0BCFF',
  },
  dark: {
    // Primary colors
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',
    
    // Secondary colors
    secondary: '#CCC2DC',
    onSecondary: '#332D41',
    secondaryContainer: '#4A4458',
    onSecondaryContainer: '#E8DEF8',
    
    // Tertiary colors
    tertiary: '#EFB8C8',
    onTertiary: '#492532',
    tertiaryContainer: '#633B48',
    onTertiaryContainer: '#FFD8E4',
    
    // Error colors
    error: '#FFB4AB',
    onError: '#690005',
    errorContainer: '#93000A',
    onErrorContainer: '#FFDAD6',
    
    // Background colors
    background: '#141218',
    onBackground: '#E6E0E9',
    
    // Surface colors
    surface: '#141218',
    onSurface: '#E6E0E9',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    surfaceContainer: '#211F26',
    surfaceContainerLow: '#1D1B20',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    
    // Outline colors
    outline: '#938F99',
    outlineVariant: '#49454F',
    
    // Other colors
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#E6E0E9',
    inverseOnSurface: '#322F35',
    inversePrimary: '#6750A4',
  },
} as const;

// 创建 NativeWind 主题变量
export function createM3EThemeVars(mode: 'light' | 'dark') {
  const colors = M3E_COLOR_TOKENS[mode];
  
  return vars({
    // Primary colors
    '--color-primary-main': colors.primary,
    '--color-primary-on': colors.onPrimary,
    '--color-primary-container': colors.primaryContainer,
    '--color-primary-on-container': colors.onPrimaryContainer,
    
    // Secondary colors
    '--color-secondary-main': colors.secondary,
    '--color-secondary-on': colors.onSecondary,
    '--color-secondary-container': colors.secondaryContainer,
    '--color-secondary-on-container': colors.onSecondaryContainer,
    
    // Tertiary colors
    '--color-tertiary-main': colors.tertiary,
    '--color-tertiary-on': colors.onTertiary,
    '--color-tertiary-container': colors.tertiaryContainer,
    '--color-tertiary-on-container': colors.onTertiaryContainer,
    
    // Error colors
    '--color-error-main': colors.error,
    '--color-error-on': colors.onError,
    '--color-error-container': colors.errorContainer,
    '--color-error-on-container': colors.onErrorContainer,
    
    // Background colors
    '--color-background': colors.background,
    '--color-background-on': colors.onBackground,
    
    // Surface colors
    '--color-surface': colors.surface,
    '--color-surface-on': colors.onSurface,
    '--color-surface-variant': colors.surfaceVariant,
    '--color-surface-on-variant': colors.onSurfaceVariant,
    '--color-surface-container': colors.surfaceContainer,
    '--color-surface-container-low': colors.surfaceContainerLow,
    '--color-surface-container-high': colors.surfaceContainerHigh,
    '--color-surface-container-highest': colors.surfaceContainerHighest,
    
    // Outline colors
    '--color-outline': colors.outline,
    '--color-outline-variant': colors.outlineVariant,
    
    // Other colors
    '--color-shadow': colors.shadow,
    '--color-scrim': colors.scrim,
    '--color-inverse-surface': colors.inverseSurface,
    '--color-inverse-on-surface': colors.inverseOnSurface,
    '--color-inverse-primary': colors.inversePrimary,
  });
}

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'system';

// 导出主题对象供组件使用
export const M3E_THEMES = {
  light: createM3EThemeVars('light'),
  dark: createM3EThemeVars('dark'),
};
