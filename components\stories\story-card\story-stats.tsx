import React from 'react';
import { View } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/nativewind-theme-provider';

import { Text } from '@/components/base';

import { Eye, Heart } from 'lucide-react-native';

interface StoryStatsProps {
  views: number;
  likes: number;
}

export function StoryStats({ views, likes }: StoryStatsProps) {
  const { colors } = useUnifiedTheme();

  return (
    <View className="flex flex-row mt-1 gap-4">
      <View className="flex flex-row items-center gap-1">
        <Eye size={12} color={colors.onSurfaceVariant} />
        <Text className="text-xs" style={{ color: colors.onSurfaceVariant }}>
          {views}
        </Text>
      </View>

      <View className="flex flex-row items-center gap-1">
        <Heart size={12} color={colors.onSurfaceVariant} />
        <Text className="text-xs" style={{ color: colors.onSurfaceVariant }}>
          {likes}
        </Text>
      </View>
    </View>
  );
}
