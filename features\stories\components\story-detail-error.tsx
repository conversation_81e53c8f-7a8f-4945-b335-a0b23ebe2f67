import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useUnifiedTheme } from '@/lib/theme/nativewind-theme-provider';


import { Text  } from '@/components/base';
import { M3EButtonFilled } from '@/components/ui/m3e-button';

interface StoryDetailErrorProps {
  error: string;
  onRetry: () => void;
}

export function StoryDetailError({ error, onRetry }: StoryDetailErrorProps) {
  const { t } = useTranslation();
  const { colors } = useUnifiedTheme();

  return (
    <View className="flex-1 justify-center items-center p-6">
      <Text
        size="lg"
        className="text-red-600 dark:text-red-400 text-center mb-6"
      >
        {error}
      </Text>

      <M3EButtonFilled size="medium" icon="refresh" onPress={onRetry}>
        {t('tryAgain', 'Try Again')}
      </M3EButtonFilled>
    </View>
  );
}
