/**
 * NativeWind M3E 主题提供者
 * 基于 NativeWind 最佳实践实现的主题系统
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useColorScheme } from 'nativewind';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { M3E_THEMES, M3E_COLOR_TOKENS, ThemeMode } from './nativewind-m3e-theme';

// 主题上下文接口
interface M3EThemeContextType {
  /** 当前主题模式 */
  mode: 'light' | 'dark';
  /** 用户设置的主题模式 */
  userMode: ThemeMode;
  /** 主题颜色令牌 */
  colors: typeof M3E_COLOR_TOKENS.light;
  /** 是否为深色模式 */
  isDark: boolean;
  /** 设置主题模式 */
  setTheme: (mode: ThemeMode) => void;
  /** 切换主题 */
  toggleTheme: () => void;
}

// 创建主题上下文
const M3EThemeContext = createContext<M3EThemeContextType | undefined>(undefined);

// 主题提供者属性
interface M3EThemeProviderProps {
  children: React.ReactNode;
  /** 默认主题模式 */
  defaultTheme?: ThemeMode;
  /** 存储键名 */
  storageKey?: string;
}

// 存储键
const THEME_STORAGE_KEY = 'm3e-theme-mode';

/**
 * M3E 主题提供者
 * 
 * 基于 NativeWind 最佳实践的主题系统，使用 CSS 变量实现动态主题切换
 * 
 * @example
 * ```tsx
 * <M3EThemeProvider defaultTheme="system">
 *   <App />
 * </M3EThemeProvider>
 * ```
 */
export function M3EThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = THEME_STORAGE_KEY,
}: M3EThemeProviderProps) {
  const { colorScheme, setColorScheme } = useColorScheme();
  const [userMode, setUserMode] = useState<ThemeMode>(defaultTheme);
  const [isLoaded, setIsLoaded] = useState(false);

  // 计算当前实际主题模式
  const currentMode: 'light' | 'dark' = 
    userMode === 'system' 
      ? (colorScheme === 'dark' ? 'dark' : 'light')
      : userMode === 'dark' 
        ? 'dark' 
        : 'light';

  const isDark = currentMode === 'dark';
  const colors = M3E_COLOR_TOKENS[currentMode];

  // 从存储加载主题设置
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(storageKey);
        if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
          setUserMode(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.warn('Failed to load theme from storage:', error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadTheme();
  }, [storageKey]);

  // 同步 NativeWind 颜色方案
  useEffect(() => {
    if (userMode === 'system') {
      setColorScheme('system');
    } else {
      setColorScheme(userMode);
    }
  }, [userMode, setColorScheme]);

  // 设置主题
  const setTheme = async (mode: ThemeMode) => {
    try {
      setUserMode(mode);
      await AsyncStorage.setItem(storageKey, mode);
    } catch (error) {
      console.warn('Failed to save theme to storage:', error);
    }
  };

  // 切换主题
  const toggleTheme = () => {
    const nextMode = currentMode === 'light' ? 'dark' : 'light';
    setTheme(nextMode);
  };

  const contextValue: M3EThemeContextType = {
    mode: currentMode,
    userMode,
    colors,
    isDark,
    setTheme,
    toggleTheme,
  };

  // 等待加载完成
  if (!isLoaded) {
    return null;
  }

  return (
    <M3EThemeContext.Provider value={contextValue}>
      <View style={M3E_THEMES[currentMode]} className="flex-1">
        {children}
      </View>
    </M3EThemeContext.Provider>
  );
}

/**
 * 使用 M3E 主题的 Hook
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { colors, isDark, toggleTheme } = useM3ETheme();
 *   
 *   return (
 *     <View className="bg-m3-surface-main">
 *       <Text className="text-m3-surface-on">Hello World</Text>
 *       <Button onPress={toggleTheme}>
 *         Switch to {isDark ? 'Light' : 'Dark'} Mode
 *       </Button>
 *     </View>
 *   );
 * }
 * ```
 */
export function useM3ETheme(): M3EThemeContextType {
  const context = useContext(M3EThemeContext);
  if (context === undefined) {
    throw new Error('useM3ETheme must be used within a M3EThemeProvider');
  }
  return context;
}

// 向后兼容的别名
export const useAppTheme = useM3ETheme;
