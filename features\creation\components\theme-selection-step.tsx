import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/nativewind-theme-provider';

import { createStyles } from './theme-selection-step.styles';
import { StoryTheme } from '@/types/story';
import ThemeSelectionCard from '@/components/creation/theme-selection-card';
import { useTranslation } from 'react-i18next';

interface ThemeSelectionStepProps {
  availableThemes: StoryTheme[];
  selectedThemes: string[];
  onThemeSelect: (themeId: string) => void;
}

export function ThemeSelectionStep({
  availableThemes,
  selectedThemes,
  onThemeSelect,
}: ThemeSelectionStepProps) {
  const { mode } = useUnifiedTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {t('selectStoryThemeTitle', '创建新故事')}
      </Text>

      <Text style={styles.description}>{t('selectStoryThemeDesc')}</Text>

      <ScrollView style={styles.scrollView}>
        <View style={styles.grid}>
          {availableThemes.map((themeItem) => (
            <ThemeSelectionCard
              key={themeItem.id}
              theme={themeItem}
              isSelected={selectedThemes.includes(themeItem.id)}
              onSelect={() => onThemeSelect(themeItem.id)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
}
